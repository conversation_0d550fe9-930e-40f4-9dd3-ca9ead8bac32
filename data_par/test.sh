#!/bin/sh

# 定义路径（请根据实际环境修改这些变量）
OUTPUT_PATH="/home/<USER>/app/data/output_radar_data"
INPUT_PATH="/home/<USER>/app/data/targetData"

# 定义远程主机信息
REMOTE_IP="**************"
REMOTE_USER="llh"
REMOTE_PATH="/media/llh/新加卷/采集数据/0812"

# 检查输出目录是否存在
if [ ! -d "$OUTPUT_PATH" ]; then
    echo "错误: 输出目录 $OUTPUT_PATH 不存在"
    exit 1
fi

# 1. 文件传输功能
echo "正在将文件从 $OUTPUT_PATH 传输到远程主机..."
scp -r "$OUTPUT_PATH"/* "$REMOTE_USER@$REMOTE_IP:$REMOTE_PATH"

if [ $? -ne 0 ]; then
    echo "警告: 文件传输失败"
fi

# 2. 清理输出目录
echo "正在清理输出目录 $OUTPUT_PATH..."
rm -rf "$OUTPUT_PATH"/*

if [ $? -eq 0 ]; then
    echo "成功清理输出目录"
else
    echo "警告: 清理输出目录时出错"
fi


# 3. 是否清理输入目录的.bin文件
read -p "是否清理输入目录中的.bin文件？(y/n): " choice
if [ "$choice" = "y" ]; then
echo "正在清理输入目录中的.bin文件..."
find "$INPUT_PATH" -name '*.bin' -type f -delete

if [ $? -eq 0 ]; then
    echo "成功清理输入目录中的.bin文件"
else
    echo "警告: 清理输入目录中的.bin文件时出错"
fi
fi

echo "文件传输和清理操作完成"