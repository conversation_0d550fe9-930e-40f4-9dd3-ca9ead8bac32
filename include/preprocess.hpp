#pragma once
#include <vector>
#include <cstddef>

void sample_data(const std::vector<double>& input, std::vector<double>& output, size_t sample_size);

void normalize_input_tensor_multithreaded(
    const std::vector<double>& input_tensor,
    std::vector<float>& output_tensor,
    size_t num_threads = 4
);

void sample_and_normalize(
    const float* input,
    size_t total_floats,
    std::vector<float>& output
);
