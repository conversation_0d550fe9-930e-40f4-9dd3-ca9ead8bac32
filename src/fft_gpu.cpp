#include "fft_gpu.hpp"
#include <stdexcept>
// #include <immintrin.h>
#include <cuda_runtime.h>
#include <cstring>
#include <unordered_map>

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols)
    : ROWS(rows), COLS(cols) {
    int rank = 1;
    int n[1] = { ROWS };
    int istride = COLS;
    int ostride = COLS;
    int idist = 1;
    int odist = 1;
    int inembed[1] = { ROWS };
    int onembed[1] = { ROWS };
    int batch = COLS;

    if (cufftPlanMany(&fft_col_plan_, rank, n,
                      inembed, istride, idist,
                      onembed, ostride, odist,
                      CUFFT_C2C, batch) != CUFFT_SUCCESS) {
        throw std::runtime_error("Failed to create cuFFT PlanMany for column FFT");
    }
}

FFTGPUOptimizer::~FFTGPUOptimizer() {
    cufftDestroy(fft_col_plan_);
}

void FFTGPUOptimizer::performColumnwiseFFT_GPU(float* d_complexData) {
    cufftComplex* d_data = reinterpret_cast<cufftComplex*>(d_complexData);
    if (cufftExecC2C(fft_col_plan_, d_data, d_data, CUFFT_FORWARD) != CUFFT_SUCCESS) {
        throw std::runtime_error("cuFFT column-wise execution failed");
    }
}

float* FFTGPUOptimizer::allocDeviceBuffer(size_t elements) {
    float* d_ptr = nullptr;
    if (cudaMalloc(&d_ptr, elements * sizeof(float)) != cudaSuccess) {
        throw std::runtime_error("CUDA malloc failed");
    }
    return d_ptr;
}

void FFTGPUOptimizer::freeDeviceBuffer(float* d_ptr) {
    cudaFree(d_ptr);
}

static ColumnSegment extractColumnSegment(
    const float* h_complexData,
    int col,
    int row)      // 原始行号
{
    int segment_start = 0;
    int segment_length = 0;

    // 根据 row 判断所在分段
    if (row >= 0 && row < 512) {
        segment_start = 0;
        segment_length = 512;
    } else if (row >= 512 && row < 768) {
        segment_start = 512;
        segment_length = 256;
    } else if (row >= 768 && row < 896) {
        segment_start = 768;
        segment_length = 128;
    } else if (row >= 896 && row < 960) {
        segment_start = 896;
        segment_length = 64;
    } else if (row >= 960 && row < 1024) {
        segment_start = 960;
        segment_length = 64;
    } else {
        throw std::runtime_error("Invalid y coordinate");
    }

    ColumnSegment seg;
    seg.segment_length = segment_length;
    seg.segment_start  = segment_start;
    seg.data.resize(segment_length);

    for (int r = 0; r < segment_length; ++r) {
        int src_row = segment_start + r;
        size_t src_idx = src_row * 2048 * 2 + col * 2;
        seg.data[r].x = h_complexData[src_idx];
        seg.data[r].y = h_complexData[src_idx + 1];
    }
    return seg;
}

// 单列FFT
std::vector<std::complex<float>> FFTGPUOptimizer::performColumnwiseFFTForCenters(
    const std::vector<std::pair<int, int>>& centers,
    const float* h_complexData)
{
    std::vector<std::complex<float>> results(centers.size());

    for (size_t i = 0; i < centers.size(); ++i) {
        int col = centers[i].first;
        int row = centers[i].second;

        // 1. 提取列数据段
        ColumnSegment seg = extractColumnSegment(h_complexData, col, row);

        // 2. 拷贝到设备
        cufftComplex* d_data = nullptr;
        if (cudaMalloc(&d_data, seg.segment_length * sizeof(cufftComplex)) != cudaSuccess)
            throw std::runtime_error("CUDA malloc failed");

        if (cudaMemcpy(d_data, seg.data.data(),
                       seg.segment_length * sizeof(cufftComplex),
                       cudaMemcpyHostToDevice) != cudaSuccess) {
            cudaFree(d_data);
            throw std::runtime_error("CUDA memcpy failed");
        }

        // 3. 创建并执行 FFT
        cufftHandle plan;
        if (cufftPlan1d(&plan, seg.segment_length, CUFFT_C2C, 1) != CUFFT_SUCCESS) {
            cudaFree(d_data);
            throw std::runtime_error("cuFFT plan failed");
        }

        cufftComplex* d_fft = nullptr;
        if (cudaMalloc(&d_fft, seg.segment_length * sizeof(cufftComplex)) != cudaSuccess) {
            cufftDestroy(plan);
            cudaFree(d_data);
            throw std::runtime_error("CUDA malloc failed");
        }

        if (cufftExecC2C(plan, d_data, d_fft, CUFFT_FORWARD) != CUFFT_SUCCESS) {
            cufftDestroy(plan);
            cudaFree(d_data);
            cudaFree(d_fft);
            throw std::runtime_error("cuFFT exec failed");
        }

        // 4. 取回结果并定位到 row 所在位置
        std::vector<cufftComplex> h_fft(seg.segment_length);
        if (cudaMemcpy(h_fft.data(), d_fft,
                       seg.segment_length * sizeof(cufftComplex),
                       cudaMemcpyDeviceToHost) != cudaSuccess) {
            cufftDestroy(plan);
            cudaFree(d_data);
            cudaFree(d_fft);
            throw std::runtime_error("CUDA memcpy failed");
        }

        int local_row = row - seg.segment_start;
        results[i] = std::complex<float>(h_fft[local_row].x, h_fft[local_row].y);

        // 5. 清理
        cufftDestroy(plan);
        cudaFree(d_data);
        cudaFree(d_fft);
    }

    return results;
}