#pragma once

#include <vector>
#include <complex>
#include <cufft.h>

struct ColumnSegment {
    std::vector<cufftComplex> data;   // 列数据
    int segment_length;               // 本段实际行数
    int segment_start;                // 全局起始行号
};

class FFTGPUOptimizer {
public:
    FFTGPUOptimizer(int rows, int cols);
    ~FFTGPUOptimizer();

    // 复数FFT（GPU列方向执行）
    void performColumnwiseFFT_GPU(float* d_complexData);

    // 设备内存管理
    float* allocDeviceBuffer(size_t elements);
    void freeDeviceBuffer(float* d_ptr);

    // 维度访问
    int getRows() const { return ROWS; }
    int getCols() const { return COLS; }

    // 对特定列执行一维FFT并返回对应点的FFT结果
    std::vector<std::complex<float>> performColumnwiseFFTForCenters(
        const std::vector<std::pair<int,int>>& centers,
        const float* h_complexData);

private:
    int ROWS;
    int COLS;
    cufftHandle fft_col_plan_;

    // 辅助函数：从CPU数据中提取特定列
    void extractColumnsFromCPU(
        const float* h_complexData,
        const std::vector<std::pair<int,int>>& centers,
        std::vector<std::vector<cufftComplex>>& columns_data
    );

};
